<?php

namespace App\Models\Finance;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentVoucher extends Model
{
    protected $fillable = [
        'voucher_no',

        'paid_at',
        'paid_to_id',

        'amount',
        'currency_code',
        'exchange_rate',

        'payment_method',
        'check_no',

        'description',

        'created_by_id',
        'checked_by_id',
        'approved_by_id',
    ];

    protected function casts()
    {
        return [
            'amount' => 'float',
            'exchange_rate' => 'float',
        ];
    }

    public function paidTo(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function checkedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getNextNumber(): string
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "PV-{$periodYear}";

        $lastBill = static::query()
            ->where('voucher_no', 'like', $prefix . '%')
            ->orderBy('voucher_no', 'desc')
            ->first();
        $lastNumber = $lastBill ?
        (int) str_replace($prefix, '', $lastBill->voucher_no)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
