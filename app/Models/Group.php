<?php

namespace App\Models;

use App\Contracts\IsActivitySubject;
use App\Enums\ContactType;
use App\Enums\ExpenseGroup;
use App\Enums\RoomCapacity;
use App\Models\Finance\CashCategory;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use App\Models\Finance\UserCash;
use App\Models\Traits\DefaultLogOptions;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Kenepa\ResourceLock\Models\Concerns\HasLocks;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Umrahservice\Groups\Enums\GroupProgress;
use Umrahservice\Groups\Enums\GroupStatus;
use Umrahservice\Groups\Models\Group as UmrahserviceGroup;

/**
 * @property-read Collection<GroupHotel> $group_hotels
 */
class Group extends UmrahserviceGroup implements IsActivitySubject
{
    use DefaultLogOptions;
    use HasFactory;
    use HasLocks;
    use LogsActivity;
    use Traits\Metable;

    protected $fillable = [
        'token',

        'invoice_number', // @deprecated
        'invoice_amount', // @deprecated

        'customer_id',

        'services',

        'name',
        'number',

        'arrival_date',
        'departure_date',

        'total_pax',

        'tour_leader_name', // deprecated
        'tour_leader_phone', // deprecated
        'muthowwif_name', // deprecated
        'muthowwif_phone', // deprecated

        'tour_leader_id',

        'mutawif_id',
        'mutawif_2_id',
        'mutawif_3_id',

        'flight_arr_id', // deprecated
        'flight_dep_id', // deprecated

        'airport_handler_arr_id', // @deprecated
        'airport_handler_dep_id', // @deprecated

        'room_double_count', // deprecated
        'room_triple_count', // deprecated
        'room_quad_count', // deprecated

        'transport_id',
        'transport_bus_count',
        'transport_contact_name', // deprecated
        'transport_contact_phone', // deprecated

        'institution_id',
        'institution_contact_name',
        'institution_contact_phone',

        'hotel_handlers',

        'meals',

        'status',
        'progress',

        'meta',

        'created_by_id',
        'updated_by_id',
    ];

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->name ??= $model->arrival_date
                ? $model->arrival_date->format('j F Y')
                : now()->format('j F Y');
        });
    }

    public function getActivitySubjectDescription(Activity $activity): string
    {
        return "Group #{$this->id}";
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function bill_items(): HasMany
    {
        return $this->hasMany(BillItem::class);
    }

    public function tour_leader(): BelongsTo
    {
        return $this->belongsTo(Contact::class, 'tour_leader_id')->where('type', ContactType::TourLeader);
    }

    public function mutawif(): BelongsTo
    {
        return $this->belongsTo(User::class, 'mutawif_id')->role('Mutawif');
    }

    public function mutawif_2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'mutawif_2_id')->role('Mutawif');
    }

    public function mutawif_3(): BelongsTo
    {
        return $this->belongsTo(User::class, 'mutawif_3_id')->role('Mutawif');
    }

    public function flights(): HasMany
    {
        return $this->hasMany(GroupFlight::class);
    }

    /**
     * @deprecated
     */
    public function flight_arr(): BelongsTo
    {
        return $this->belongsTo(Flight::class, 'flight_arr_id');
    }

    /**
     * @deprecated
     */
    public function flight_dep(): BelongsTo
    {
        return $this->belongsTo(Flight::class, 'flight_dep_id');
    }

    /**
     * @deprecated
     */
    public function airport_handler_arr(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'airport_handler_arr_id');
    }

    /**
     * @deprecated
     */
    public function airport_handler_dep(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'airport_handler_dep_id');
    }

    public function manasiks(): HasMany
    {
        return $this->hasMany(Manasik::class);
    }

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class);
    }

    public function hotels(): BelongsToMany
    {
        return $this->belongsToMany(Hotel::class)
            ->withPivot([
                'check_in',
                'check_out',

                'room_single_count',
                'room_double_count',
                'room_triple_count',
                'room_quad_count',
                'room_quint_count',

                'is_confirmed',
            ])
            ->using(GroupHotel::class);
    }

    public function group_hotels(): HasMany
    {
        return $this->hasMany(GroupHotel::class);
    }

    public function vehicles(): BelongsToMany
    {
        return $this->belongsToMany(Vehicle::class)
            ->using(GroupVehicle::class)
            ->withPivot('pax_count', 'count');
    }

    public function group_vehicles(): HasMany
    {
        return $this->hasMany(GroupVehicle::class);
    }

    public function itineraries(): HasMany
    {
        return $this->hasMany(Itinerary::class);
    }

    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class);
    }

    public function group_data(): HasOne
    {
        return $this->hasOne(GroupData::class);
    }

    public function cashes(): HasMany
    {
        return $this->hasMany(GroupCash::class, 'group_id');
    }

    public function user_cashes(): HasMany
    {
        return $this->hasMany(UserCash::class, 'group_id');
    }

    public function pilgrims(): BelongsToMany
    {
        return $this->belongsToMany(Pilgrim::class)->withTimestamps()
            ->withPivot(['room_id', 'is_tour_leader']);
    }

    public function group_pilgrims(): HasMany
    {
        return $this->hasMany(GroupPilgrim::class);
    }

    public function rooms(): HasMany
    {
        return $this->hasMany(Room::class);
    }

    public function created_by(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function updated_by(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeWithLatestLogs($query)
    {
        // TODO:
        $query
            ->addSelect(DB::raw("(SELECT `causer_id` FROM `activity_log` WHERE `groups`.`id` = `activity_log`.`subject_id` AND `activity_log`.`subject_type` = 'user') AS `created_by`"));
    }

    public function scopeCurrentPeriod($query, $strict = false)
    {
        $period = current_period();

        $query
            ->where(function ($query) use ($strict, $period) {
                $query
                    ->whereBetween('arrival_date', [
                        $period->date_start->format('Y-m-d') . ' 00:00:00',
                        $period->date_end->format('Y-m-d') . ' 23:59:59',
                    ])
                    ->when(! $strict, function ($query) {
                        $query->orWhereNull('arrival_date');
                    });
            });
    }

    public function isInCurrentPeriod()
    {
        $period = current_period();
        /** @var Carbon|null */
        $date = $this->arrival_date;

        return $date
            ? $date->gte($period->date_start) && $date->lte($period->date_end)
            : true;
    }

    public function getPeriod()
    {
        return Period::query()
            ->whereDate('date_start', '<', $this->arrival_date)
            ->whereDate('date_end', '>', $this->arrival_date)
            ->first();
    }

    public function mutawifs(): Attribute
    {
        return Attribute::make(
            get: fn () => collect([
                $this->mutawif, $this->mutawif_2, $this->mutawif_3,
            ])->filter()->values()
        );
    }

    public function getHotelHandlersAttribute()
    {
        $handlers = $this->attributes['hotel_handlers'] ?? '[]';
        $handlers = collect(json_decode($handlers, true));
        $users = User::query()
            ->whereIn('id', $handlers->pluck('handler_id'))
            ->get()
            ->keyBy('id');

        return $handlers
            ->map(function ($item) use ($users) {
                if (isset($item['handler_id'])) {
                    $user = $users[$item['handler_id']] ?? null;
                    $item['name'] = $user ? $user->name : '-';
                    $item['phone'] = $user ? $user->phone : '-';
                }

                return $item;
            })
            ->toArray();
    }

    public function getClosingReportData()
    {
        $this->load([
            'customer',
            'flight_arr',
            'flight_dep',
        ]);

        $payments = InvoicePayment::query()->whereIn('invoice_id', $this->invoices->pluck('id'))->get();
        $total_income = $payments->sum(fn ($i) => $i->amount * $i->exchange_rate) ?? 0;

        $bill_items = BillItem::query()
            ->with(['bill'])
            ->where('group_id', $this->id)
            ->get();

        $total_expense = $bill_items->sum(fn ($i) => ($i->unit_price * $i->quantity * (1 + $i->vat / 100)) * $i->bill->exchange_rate);

        $categories = CashCategory::query()
            ->with(['group_cashes' => fn ($q) => $q->where('group_id', $this->id)
                ->where('division', 'operator')])
            ->whereRaw("COALESCE(`group`, '') != ?", [ExpenseGroup::VendorPayment])
            ->where(fn ($q) => $q->where('type', 'out')
                ->orWhere('type', 'in_out'))
            ->whereHas('group_cashes', fn ($q) => $q->where('group_id', $this->id)
                ->where('division', 'operator'))
            ->get();

        $expenses = [];
        foreach ($categories as $cat) {
            $expenses[$cat->name] = $cat->group_cashes->sum('cash_out_c') - $cat->group_cashes->sum('cash_in_c');
        }

        $expenses['Airport Handling'] = UserCash::query()
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->where('group', ExpenseGroup::AirportHandling))
            ->where('group_id', $this->id)
            ->sum(DB::raw('amount * exchange_rate'));

        $expenses['Operational Check-In/Out Team'] = UserCash::query()
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->where('group', ExpenseGroup::HotelCheckInOut))
            ->where('group_id', $this->id)
            ->sum(DB::raw('amount * exchange_rate'));

        $expenses['Other expenses'] = GroupCash::query()
            ->where('division', 'operator')
            ->where('group_id', $this->id)
            ->whereNull('category_id')
            ->sum('cash_out_c');
        $expenses['Other expenses'] += UserCash::query()
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->whereNull('group'))
            ->where('group_id', $this->id)
            ->sum(DB::raw('amount * exchange_rate'));

        $expenses = collect($expenses)->filter(fn ($value) => $value > 0);
        $total_expense += $expenses->sum();
        $expenses = $expenses->toArray();

        $cashes = collect(GroupCash::DIVISIONS)
            ->mapWithKeys(function ($value, $key) {
                $items = GroupCash::query()
                    ->with(['category.parent'])
                    ->whereHas('category', fn ($query) => $query
                        ->whereRaw("COALESCE(`group`, '') != ?", [ExpenseGroup::VendorPayment]))
                    ->where('division', $key)
                    ->where('group_id', $this->id)
                    ->orderBy('cashed_at')
                    ->get();

                return [$value => $items];
            })
            ->filter(fn ($value) => filled($value))
            ->all();
        $cashes['Airport Handling'] = UserCash::query()
            ->with(['category.parent'])
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->where('group', ExpenseGroup::AirportHandling))
            ->where('group_id', $this->id)
            ->orderBy('cashed_at')
            ->get();
        $cashes['Operational Check-In/Out Team'] = UserCash::query()
            ->with(['category.parent'])
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->where('group', ExpenseGroup::HotelCheckInOut))
            ->where('group_id', $this->id)
            ->orderBy('cashed_at')
            ->get();
        $cashes['Other Expenses'] = UserCash::query()
            ->with(['category.parent'])
            ->where('type', 'c')
            ->whereHas('category', fn ($query) => $query
                ->whereNull('group'))
            ->where('group_id', $this->id)
            ->orderBy('cashed_at')
            ->get();

        return compact('payments', 'total_income', 'expenses', 'total_expense', 'cashes', 'bill_items');
    }

    public function duplicate(): self
    {
        $clone = $this->replicate(['bill_items_count']);
        $clone->created_by_id = auth()->user()->id;
        $clone->save();

        $manasiks = [];
        foreach ($this->manasiks as $manasik) {
            $manasiks[] = $manasik->replicate(['group_id']);
        }
        $clone->manasiks()->saveMany($manasiks);

        $flights = [];
        foreach ($this->flights as $manasik) {
            $flights[] = $manasik->replicate(['group_id']);
        }
        $clone->flights()->saveMany($flights);

        $group_hotels = [];
        foreach ($this->group_hotels as $group_hotel) {
            $gh = $group_hotel->replicate(['group_id']);
            $gh->is_confirmed = false;
            $gh->broker_id = null;
            $gh->confirmation_file = null;

            $group_hotels[] = $gh;
        }
        $clone->group_hotels()->saveMany($group_hotels);

        $itineraries = [];
        foreach ($this->itineraries as $itinerary) {
            $itineraries[] = $itinerary->replicate(['group_id']);
        }
        $clone->itineraries()->saveMany($itineraries);

        return $clone;
    }

    public static function tableActionUpdateProgress(): Tables\Actions\Action
    {
        return Tables\Actions\Action::make('update_progress')
            ->color('warning')
            ->form([
                Forms\Components\Select::make('progress')
                    ->options(GroupProgress::class),
            ])
            ->mountUsing(fn ($record, $form) => $form->fill([
                'progress' => $record->progress,
            ]))
            ->action(function ($record, $data) {
                $data['progress'] ??= GroupProgress::Waiting;
                $record->update($data);
            })
            ->modalWidth('sm')
            ->visible(fn ($record) => $record->status == GroupStatus::Confirmed &&
                auth('web')->user()->hasRole(['Admin', 'Operator', 'Finance']));
    }

    public function generateRooms()
    {
        $this->rooms()->delete();
        $this->group_pilgrims()->update(['room_id' => null]);

        $groupHotels = $this->group_hotels
            ->groupBy(function ($i) {
                return $i->hotel->city;
            })
            ->map(function ($i) {
                return [
                    'single' => $i->sum('room_single_count'),
                    'double' => $i->sum('room_double_count'),
                    'triple' => $i->sum('room_triple_count'),
                    'quad' => $i->sum('room_quad_count'),
                    'quint' => $i->sum('room_quint_count'),
                ];
            });

        $counts = [
            1 => $groupHotels->max('single'),
            2 => $groupHotels->max('double'),
            3 => $groupHotels->max('triple'),
            4 => $groupHotels->max('quad'),
            5 => $groupHotels->max('quint'),
        ];

        $number = 1;
        foreach ($counts as $capacity => $count) {
            $room_capacity = RoomCapacity::from($capacity);
            for ($i = 0; $i < $count; $i++) {
                $this->rooms()->create([
                    'name' => $room_capacity->getLabel() . ' ' . ($i + 1),
                    'number' => sprintf('%03d', $number++),
                    'capacity' => $capacity,
                ]);
            }
        }
    }

    public function logEvent(string $eventName, ?string $description = null)
    {
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->event($eventName)
            ->log($description ?? $eventName);
    }
}
