<?php

namespace App\Filament\Finance\Resources;

use App\Actions\Invoice\BulkGeneratePDF;
use App\Enums\InvoiceStatus;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\InvoiceResource\Pages;
use App\Filament\Finance\Resources\InvoiceResource\Widgets;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Finance\Product;
use App\Models\Finance\ProductCategory;
use App\Models\Group;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $recordTitleAttribute = 'invoice_number';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 20;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->columns()
                            ->schema(static::getFormSchema()),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Placeholder::make('created_at')
                                    ->label('Created at')
                                    ->content(fn ($record) => $record->created_at?->diffForHumans()),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('Last modified at')
                                    ->content(fn ($record) => $record->updated_at?->diffForHumans()),
                            ])
                            ->hidden(fn ($record) => $record === null),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('invoice_number')
                                    ->default(fn () => Invoice::getNextInvoiceNumber())
                                    ->unique(ignoreRecord: true)
                                    ->required(),

                                Forms\Components\DatePicker::make('invoice_date')
                                    ->default(now())
                                    ->required(),

                                Forms\Components\DatePicker::make('due_date')
                                    ->default(now()->addDays(15))
                                    ->required(),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Select::make('currency_code')
                                    ->required()
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->default(1),
                            ]),
                    ]),

                Forms\Components\Section::make('Invoice items')
                    ->schema(static::getFormSchema('items')),
            ])
            ->columns(3);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema(static::getInfolistSchema())
                            ->columns(),
                    ])
                    ->columnSpan(['lg' => 2]),

                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('invoice_number'),
                                Infolists\Components\TextEntry::make('invoice_date')
                                    ->date(),
                                Infolists\Components\TextEntry::make('due_date')
                                    ->date(),
                            ]),

                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('currency_code')
                                    ->label('Currency'),
                                Infolists\Components\TextEntry::make('exchange_rate'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Invoice items')
                    ->schema(static::getInfolistSchema('items')),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['group', 'customer'])
                ->select([
                    'invoices.*',
                    DB::raw('invoices.total - invoices.paid AS balance'),
                ]))
            ->recordClasses(fn ($record) => match ($record->status) {
                InvoiceStatus::Paid => 'bg-success-500/10',
                InvoiceStatus::Overdue => 'bg-danger-500/10',
                InvoiceStatus::Cancelled => 'bg-gray-500/10',
                InvoiceStatus::Unpaid => 'bg-warning-500/10',
                default => null
            })
            ->columns([
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->group
                        ? $record->group->name ?? 'Group #' . $record->group->id
                        : null)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('invoice_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Items')
                    ->badge(),
                Tables\Columns\TextColumn::make('total')
                    ->label('Total invoice')
                    ->sortable()
                    ->currencyAuto(),
                Tables\Columns\TextColumn::make('paid')
                    ->label('Total paid')
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('balance')
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge(),
                Tables\Columns\CheckboxColumn::make('is_published')
                    ->label('Published'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->options(Customer::query()->orderBy('name')->pluck('name', 'id'))
                    ->searchable(),
                Group::tableFilterGroup(),
                Invoice::getDateTableFilter(today()->startOfYear()),
                Tables\Filters\SelectFilter::make('status')
                    ->options(InvoiceStatus::class)
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Invoice::getSendTableAction(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\ViewAction::make(),
                        Tables\Actions\EditAction::make(),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('cancel')
                            ->label('Cancel')
                            ->icon('heroicon-m-x-mark')
                            ->color('danger')
                            ->action(function ($record, $data) {
                                $record->update([
                                    ...$data,
                                    'status' => InvoiceStatus::Cancelled,
                                ]);
                            })
                            ->modalHeading('Cancel Invoice')
                            ->form([
                                Forms\Components\Textarea::make('cancellation_note')
                                    ->required(),
                            ])
                            ->requiresConfirmation()
                            ->visible(fn ($record) => $record->status !== InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('payments')
                            ->icon('heroicon-m-banknotes')
                            ->url(fn ($record) => static::getUrl('payments', ['record' => $record])),
                        Tables\Actions\Action::make('refunds')
                            ->icon('heroicon-m-receipt-refund')
                            ->url(fn ($record) => static::getUrl('refunds', ['record' => $record]))
                            ->visible(fn ($record) => $record->status === InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePDF::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('invoice_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
            'payments' => Pages\Payments::route('/{record}/payments'),
            'refunds' => Pages\Refunds::route('/{record}/refunds'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewInvoice::class,
            Pages\EditInvoice::class,
            Pages\Payments::class,
            Pages\Refunds::class,
        ]);
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\InvoiceOverview::class,
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['invoice_number', 'subject', 'customer.name', 'group.name'];
    }

    public static function getNavigationBadge(): ?string
    {
        $count = static::getEloquentQuery()
            ->notFullyPaid()
            ->count();

        return $count > 0 ? $count : null;
    }

    public static function getNavigationBadgeColor(): string | array | null
    {
        return 'warning';
    }

    public static function getFormSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Forms\Components\Repeater::make('items')
                    ->relationship()
                    ->orderColumn('order_column')
                    ->reorderableWithButtons()
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Item')
                                    ->relationship('product', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->requiredWithout('description')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set, $get) {
                                        $item = Product::query()->find($state);

                                        if ($item) {
                                            $set('name', $item->name ?? '');
                                            $set('description', $item->description ?? '');
                                            $set('unit_price', ($item->unit_price ?? 0) / ($get('../../exchange_rate') ?? 1));
                                        }
                                    })
                                    ->createOptionForm([
                                        Forms\Components\Select::make('category_id')
                                            ->label('Category')
                                            ->options(ProductCategory::query()->pluck('name', 'id'))
                                            ->searchable(),
                                        Forms\Components\TextInput::make('name')
                                            ->required(),
                                        Forms\Components\Textarea::make('description'),
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Price')
                                            ->numeric()
                                            ->prefix('SAR')
                                            ->required(),
                                    ])
                                    ->createOptionAction(fn ($action) => $action->modalWidth('sm')),
                                Forms\Components\Hidden::make('name'),
                                Forms\Components\Textarea::make('description')
                                    ->rows(2)
                                    ->hiddenLabel()
                                    ->extraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ])
                            ->columnSpan(['md' => 5]),

                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->default(1)
                            ->live()
                            ->columnSpan(['md' => 2])
                            ->required(),

                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\TextInput::make('unit_price')
                                    ->label('Unit Price')
                                    ->numeric()
                                    ->required()
                                    ->live()
                                    ->prefix(fn ($get) => $get('../../currency_code') ?? 'SAR'),
                                Forms\Components\Placeholder::make('subtotal')
                                    ->extraAttributes([
                                        'class' => 'tabular-nums',
                                    ])
                                    ->content(fn ($get) => money(floatval($get('unit_price') ?? 0) * floatval($get('quantity') ?? 0), $get('../../currency_code') ?? 'SAR', true)),
                            ])
                            ->columnSpan(['md' => 3]),
                    ])
                    ->defaultItems(1)
                    ->hiddenLabel()
                    ->columns(['md' => 10])
                    ->required(),
            ];
        }

        return [
            Forms\Components\Select::make('customer_id')
                ->relationship('customer', 'name')
                ->searchable()
                ->preload()
                ->required()
                ->createOptionForm([
                    Forms\Components\TextInput::make('name')
                        ->required(),
                    Forms\Components\TextInput::make('email')
                        ->required()
                        ->email()
                        ->unique(),
                    Forms\Components\TextInput::make('phone'),
                ])
                ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                    return $action
                        ->modalHeading('Create customer')
                        ->modalSubmitActionLabel('Create customer')
                        ->modalWidth('lg');
                }),

            Forms\Components\Select::make('group_id')
                ->label('Group')
                ->relationship('group', 'name', fn ($query, $get) => $query
                    ->select(['id', DB::raw("COALESCE(`name`, CONCAT('Group #', `id`)) as `name`")])
                    ->where('customer_id', $get('customer_id')))
                ->searchable()
                ->preload(),

            Forms\Components\Select::make('package_id')
                ->label('Package')
                ->relationship('package', 'title', fn ($query, $get) => $query
                    ->select([
                        'id',
                        DB::raw("
                            COALESCE(
                                CONCAT('#', `id`, ' - ', `title`),
                                CONCAT('Package #', `id`)
                            ) as `title`
                        "),
                    ])
                    ->where('customer_id', $get('customer_id')))
                ->searchable()
                ->preload()
                ->columnSpanFull(),

            Forms\Components\TextInput::make('subject')
                ->columnSpanFull(),

            Forms\Components\Textarea::make('notes')
                ->rows(5)
                ->columnSpanFull(),

            Forms\Components\Textarea::make('terms')
                ->label('Terms & conditions')
                ->default(Invoice::DEFAULT_TERMS)
                ->rows(10)
                ->columnSpanFull(),
        ];
    }

    public static function getInfolistSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Infolists\Components\RepeatableEntry::make('items')
                    ->contained(false)
                    ->schema([
                        Infolists\Components\Group::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('name')
                                    ->label('Item'),
                                Infolists\Components\TextEntry::make('description')
                                    ->hiddenLabel()
                                    ->formatStateUsing(fn ($state) => nl2br($state))
                                    ->html(),
                            ])
                            ->columnSpan(['md' => 5]),

                        Infolists\Components\TextEntry::make('quantity')
                            ->columnSpan(['md' => 2]),

                        Infolists\Components\TextEntry::make('unit_price')
                            ->label('Unit Price')
                            ->formatStateUsing(fn ($state, $record) => money($state, $record->invoice?->currency_code ?? 'SAR', true))
                            ->columnSpan(['md' => 3]),
                    ])
                    ->hiddenLabel()
                    ->columns(['md' => 10]),
            ];
        }

        return [
            Infolists\Components\TextEntry::make('customer.name'),
            Infolists\Components\TextEntry::make('subject'),
            Infolists\Components\TextEntry::make('notes')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),
            Infolists\Components\TextEntry::make('terms')
                ->label('Terms & conditions')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),
        ];
    }
}
