<?php

namespace App\Filament\Finance\Resources;

use App\Enums\PaymentMethod;
use App\Filament\Finance\Resources\PaymentVoucherResource\Pages;
use App\Models\Currency;
use App\Models\Finance\PaymentVoucher;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PaymentVoucherResource extends Resource
{
    protected static ?string $model = PaymentVoucher::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Voucher Information')
                    ->schema([
                        Forms\Components\TextInput::make('voucher_no')
                            ->label('Voucher Number')
                            ->default(fn () => PaymentVoucher::getNextNumber())
                            ->disabled()
                            ->dehydrated()
                            ->required(),

                        Forms\Components\DateTimePicker::make('paid_at')
                            ->label('Payment Date')
                            ->default(now())
                            ->required()
                            ->maxDate(today()->addDay()),

                        Forms\Components\Select::make('paid_to_id')
                            ->label('Paid To')
                            ->relationship('paidTo', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Payment Details')
                    ->schema([
                        Forms\Components\ToggleButtons::make('payment_method')
                            ->label('Payment Method')
                            ->options(PaymentMethod::class)
                            ->default(PaymentMethod::Cash->value)
                            ->grouped()
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('check_no')
                            ->label('Check Number')
                            ->maxLength(255)
                            ->visible(fn ($get) => $get('payment_method') === PaymentMethod::BankTransfer->value),

                        Forms\Components\Select::make('currency_code')
                            ->label('Currency')
                            ->options(Currency::getOptions())
                            ->default('SAR')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, $set) {
                                $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                            }),

                        Forms\Components\TextInput::make('exchange_rate')
                            ->label('Exchange Rate')
                            ->numeric()
                            ->default(1)
                            ->required()
                            ->live()
                            ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null),

                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->numeric()
                            ->required()
                            ->prefix(fn ($get) => $get('currency_code') ?? 'SAR')
                            ->step(0.01),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(1000)
                            ->rows(3),
                    ]),

                Forms\Components\Section::make('Approval')
                    ->schema([
                        Forms\Components\Select::make('created_by_id')
                            ->label('Created By')
                            ->relationship('createdBy', 'name')
                            ->default(fn () => auth()->id())
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Select::make('checked_by_id')
                            ->label('Checked By')
                            ->relationship('checkedBy', 'name')
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('approved_by_id')
                            ->label('Approved By')
                            ->relationship('approvedBy', 'name')
                            ->searchable()
                            ->preload(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('voucher_no')
                    ->label('Voucher No.')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes([
                        'class' => 'tabular-nums',
                    ]),

                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Payment Date')
                    ->dateTime('M j, Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('paidTo.name')
                    ->label('Paid To')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Method')
                    ->badge()
                    ->formatStateUsing(fn ($state) => PaymentMethod::tryFrom($state)?->getLabel() ?? $state),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->currencyAuto()
                    ->sortable(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    }),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Created By')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('checkedBy.name')
                    ->label('Checked By')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('approvedBy.name')
                    ->label('Approved By')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Payment Method')
                    ->options(PaymentMethod::class),

                Tables\Filters\SelectFilter::make('paid_to_id')
                    ->label('Paid To')
                    ->relationship('paidTo', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('paid_at')
                    ->form([
                        Forms\Components\DatePicker::make('paid_from')
                            ->label('Payment Date From'),
                        Forms\Components\DatePicker::make('paid_until')
                            ->label('Payment Date Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['paid_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('paid_at', '>=', $date),
                            )
                            ->when(
                                $data['paid_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('paid_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('paid_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePaymentVouchers::route('/'),
        ];
    }
}
