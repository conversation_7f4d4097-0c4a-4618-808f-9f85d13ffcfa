<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Pages;

use App\Actions\Invoice\BulkGeneratePaymentPDF;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentMethod;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\InvoicePayment;
use Awcodes\Shout\Components\Shout;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

class Payments extends ManageRelatedRecords
{
    protected static string $resource = InvoiceResource::class;

    protected static string $relationship = 'payments';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Payments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Shout::make('deposit_balance_notice')
                    ->type('info')
                    ->content(fn ($get) => 'Deposit Balance: ' . money($get('deposit_balance'), 'SAR', true))
                    ->columnSpanFull()
                    ->visible(fn ($get, $context) => $context === 'create' && ($get('deposit_balance') > 0 || $get('payment_method') === 'deposit')),
                Forms\Components\Hidden::make('deposit_balance')
                    ->default(0),
                Forms\Components\Hidden::make('currency_code')
                    ->default('SAR'),
                Forms\Components\ToggleButtons::make('payment_method')
                    ->grouped()
                    ->options(PaymentMethod::class)
                    ->default(PaymentMethod::Cash->value)
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state === 'deposit') {
                            $set('cash_account_id', CashAccount::query()
                                ->where('code', config('finance.coa.customer_deposit'))
                                ->first()->id);
                        }
                    }),
                Forms\Components\Select::make('cash_account_id')
                    ->label('Account')
                    ->relationship('cash_account', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->disabled(fn ($get) => $get('payment_method') === 'deposit'),
                Forms\Components\DateTimePicker::make('paid_at')
                    ->label('Date')
                    ->required(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->required()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR'),

                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),

                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('invoice/payments')
                    ->visibility('public'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('payment')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.name')
                    ->label('Account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mountUsing(function ($record, $form) {
                        $customer = $this->getOwnerRecord()->customer;
                        $depositBalance = $customer->deposit_balance;
                        $accountId = CashAccount::query()
                            ->where('code', $depositBalance > 0 ? config('finance.coa.customer_deposit') : config('finance.coa.main_cash'))
                            ->value('id');
                        $form->fill([
                            'deposit_balance' => $depositBalance,
                            'payment_method' => $depositBalance > 0 ? 'deposit' : 'cash',
                            'paid_at' => Carbon::now(),
                            'cash_account_id' => $accountId,
                            'currency_code' => $this->getOwnerRecord()->currency_code,
                            'exchange_rate' => 1 / Currency::getExchangeRate($this->getOwnerRecord()->currency_code),
                        ]);
                    })
                    ->visible(fn ($livewire) => $livewire->getOwnerRecord()->status !== InvoiceStatus::Cancelled),
            ])
            ->actions([
                InvoicePayment::getReceiptTableAction(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePaymentPDF::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('paid_at', 'desc');
    }
}
