<?php

namespace App\Filament\Resources\UserCashResource\Widgets;

use App\Filament\Resources\UserCashResource\Pages\ManageUserCashes;
use App\Models\Finance\UserCash;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class UserCashOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected function getTablePage(): string
    {
        return ManageUserCashes::class;
    }

    protected function getStats(): array
    {
        $data = $this->getData();

        return [
            Stat::make('Income', money($data->income ?? 0, 'SAR', true)),
            Stat::make('Expense', money($data->expense ?? 0, 'SAR', true)),
            Stat::make('Balance', money($data->balance ?? 0, 'SAR', true)),
        ];

        return [];
    }

    protected function getData()
    {
        $auth_user = auth()->user();

        if ($auth_user->can('user-cashes.viewAny')) {
            $user_id = $this->tableFilters['user_id']['value'] ?? null;
        } else {
            $user_id = $auth_user->id;
        }
        $group_id = $this->tableFilters['group']['group_id'] ?? null;

        return UserCash::query()
            ->when($user_id, fn ($query) => $query->where('user_id', $user_id))
            ->when($group_id, fn ($query) => $query->where('group_id', $group_id))
            ->select([
                DB::raw("SUM(IF(`type` = 'd', `amount_c`, 0)) AS income"),
                DB::raw("SUM(IF(`type` = 'c', `amount_c`, 0)) AS expense"),
                DB::raw("SUM(IF(`type` = 'd', `amount_c`, -1 * `amount_c`)) AS balance"),
            ])
            ->first();
    }
}
