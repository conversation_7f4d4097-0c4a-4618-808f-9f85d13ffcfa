<?php

namespace App\Filament\Finance\Resources;

use App\Actions\Invoice\BulkGeneratePaymentPDF;
use App\Enums\PaymentMethod;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\InvoicePaymentResource\Pages;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InvoicePaymentResource extends Resource
{
    protected static ?string $model = InvoicePayment::class;

    protected static ?string $modelLabel = 'payment';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 30;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('currency_code')
                    ->default('SAR'),
                Forms\Components\Select::make('invoice_id')
                    ->label('Invoice')
                    ->relationship('invoice', 'invoice_number')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $invoice = Invoice::find($state);
                        $set('currency_code', $invoice?->currency_code ?? 'SAR');
                        $set('exchange_rate', 1 / Currency::getExchangeRate($invoice?->currency_code ?? 'SAR'));
                    }),
                Forms\Components\ToggleButtons::make('payment_method')
                    ->grouped()
                    ->options(PaymentMethod::class)
                    ->default(PaymentMethod::Cash->value)
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state === 'deposit') {
                            $set('cash_account_id', CashAccount::query()
                                ->where('code', config('finance.coa.customer_deposit'))
                                ->first()->id);
                        }
                    }),
                Forms\Components\Select::make('cash_account_id')
                    ->label('Account')
                    ->relationship('cash_account', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->disabled(fn ($get) => $get('payment_method') === 'deposit'),
                Forms\Components\DateTimePicker::make('paid_at')
                    ->label('Date')
                    ->default(now())
                    ->maxDate(today()->addDay())
                    ->required(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->required()
                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR'),

                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),

                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('invoice/payments')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.')
                    ->searchable(),
                Tables\Columns\TextColumn::make('invoice.invoice_number')
                    ->label('Invoice')
                    ->url(fn ($record) => InvoiceResource::getUrl('view', ['record' => $record->invoice_id]))
                    ->searchable(),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.name')
                    ->label('Account')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto()
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('invoice_id')
                    ->label('Invoice')
                    ->relationship('invoice', 'invoice_number', fn ($query) => $query->orderByDesc('invoice_number'))
                    ->searchable()
                    ->preload(),
                InvoicePayment::getDateTableFilter(today()->startOfYear()),
                Tables\Filters\SelectFilter::make('cash_account_id')
                    ->label('Account')
                    ->options(fn () => CashAccount::query()
                        ->isCashOrBank()
                        ->orWhere('code', config('finance.coa.customer_deposit'))
                        ->orderBy('code')
                        ->get()
                        ->pluck('name', 'id'))
                    ->native(false),
            ])
            ->actions([
                // TODO:
                Tables\Actions\Action::make('send_receipt')
                    ->form([
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\Group::make()
                                    ->schema([]),
                                Forms\Components\Group::make()
                                    ->schema([
                                        Forms\Components\ViewField::make('preview')
                                            ->view('filament.forms.fields.payment-receipt'),
                                    ]),
                            ]),
                    ])
                    ->action(function () {})
                    ->visible(false),
                Tables\Actions\ActionGroup::make([
                    InvoicePayment::getReceiptTableAction(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\EditAction::make(),
                        Tables\Actions\DeleteAction::make(),
                    ])->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])->dropdown(false),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePaymentPDF::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('paid_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInvoicePayments::route('/'),
        ];
    }
}
