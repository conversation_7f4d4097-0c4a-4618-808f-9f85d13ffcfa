<?php

namespace App\Policies\Finance;

use App\Models\Finance\UserCash;
use App\Models\User;

class UserCashPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->canAny(['user-cashes.viewAny', 'finance.has-user-cash']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserCash $userCash): bool
    {
        if ($user->can('user-cashes.view')) {
            return true;
        }
        if ($user->can('finance.has-user-cash')) {
            return $user->id == $userCash->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->canAny(['user-cashes.create', 'finance.has-user-cash']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserCash $userCash): bool
    {
        if ($userCash->is_fixed) {
            return false;
        }
        if ($user->can('user-cashes.update')) {
            return true;
        }
        if ($user->can('finance.has-user-cash')) {
            return $user->id == $userCash->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserCash $userCash): bool
    {
        if ($userCash->is_fixed) {
            return false;
        }

        return $user->can('user-cashes.delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserCash $userCash): bool
    {
        if ($userCash->is_fixed) {
            return false;
        }

        return $user->can('user-cashes.restore');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserCash $userCash): bool
    {
        if ($userCash->is_fixed) {
            return false;
        }

        return $user->can('user-cashes.forceDelete');
    }
}
